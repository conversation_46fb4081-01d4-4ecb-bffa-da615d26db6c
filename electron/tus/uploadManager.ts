import { Upload } from "tus-js-client";
import Store from "electron-store";
import * as path from "path";
import { EventEmitter } from "events";
import type { UploadTask, TusUploadConfig, UploadStatus, StoreData } from "./types";
import { getAuthToken } from "../auth/authStore";
import { ArchiveManager } from "../archive/archiveManager";
import type { SmartPackingAnalysis, BatchPackingOptions } from "../archive/types";
import { tusLogger } from "./logger";

export class TusUploadManager extends EventEmitter {
  private store: Store<StoreData>;
  private uploads: Map<string, Upload> = new Map();
  private tasks: Map<string, UploadTask> = new Map();
  private config: TusUploadConfig;
  private archiveManager: ArchiveManager;

  constructor(config: TusUploadConfig, archiveManager?: ArchiveManager) {
    super();
    this.config = config;

    tusLogger.info(`📦 [TUS构造函数] 开始初始化，archiveManager参数: ${archiveManager ? "已提供" : "未提供"}`);

    // 初始化存储
    this.store = new Store<StoreData>({
      name: "tus-uploads",
      defaults: {
        tasks: {},
        settings: {
          chunkSize: config.chunkSize || 20 * 1024 * 1024, // 默认 20MB
          retryDelays: config.retryDelays || [0, 1000, 3000, 5000],
          parallelUploads: config.parallelUploads || 3,
        },
      },
    });

    // 使用传入的压缩管理器，或创建新的实例
    if (archiveManager) {
      this.archiveManager = archiveManager;
      tusLogger.info(`📦 使用外部提供的 ArchiveManager 实例，实例类型: ${archiveManager.constructor.name}`);
      tusLogger.info(`📦 外部 ArchiveManager 监听器数量: task-created=${archiveManager.listenerCount("task-created")}, task-progress=${archiveManager.listenerCount("task-progress")}`);
    } else {
      // 初始化压缩管理器（向后兼容）
      this.archiveManager = new ArchiveManager({
        compressionLevel: 0, // 仅存储，不压缩
        format: "7z",
        tempDir: require("os").tmpdir(),
        maxConcurrent: 2,
      });
      tusLogger.info(`📦 创建新的 ArchiveManager 实例`);
    }

    // 恢复未完成的上传任务
    this.restoreUnfinishedTasks();

    // 🔧 修复：启动智能打包文件清理定时器
    this.startSmartPackCleanupTimer();
  }

  /**
   * 创建上传任务
   */
  async createUploadTask(filePath: string, metadata?: Record<string, string>): Promise<string> {
    const fs = await import("fs/promises");

    try {
      const stats = await fs.stat(filePath);
      const isDirectory = stats.isDirectory();

      // 优先使用元数据中的原始文件名，如果没有则使用文件路径的基础名称
      const fileName = metadata?.originalName || path.basename(filePath);
      const taskId = this.generateTaskId();

      // 检查是否为空文件夹
      let isEmpty = false;
      if (isDirectory) {
        try {
          const entries = await fs.readdir(filePath);
          isEmpty = entries.length === 0;
        } catch (error) {
          tusLogger.warn(`无法读取目录 ${filePath}:`, error);
        }
      }

      const task: UploadTask = {
        id: taskId,
        filePath,
        fileName,
        fileSize: isDirectory ? 0 : stats.size, // 文件夹大小设为0
        progress: 0,
        status: "pending",
        bytesUploaded: 0,
        uploadSpeed: 0,
        remainingTime: 0,
        startTime: new Date(),
        metadata: { ...this.config.metadata, ...metadata },
        resumable: true,
        isFolder: isDirectory,
        isEmpty: isEmpty,
      };

      this.tasks.set(taskId, task);
      this.saveTaskToStore(task);

      this.emit("task-created", taskId, task);

      return taskId;
    } catch (error) {
      throw new Error(`无法创建上传任务: ${error}`);
    }
  }

  /**
   * 开始上传
   */
  async startUpload(taskId: string): Promise<void> {
    const task = this.tasks.get(taskId);
    if (!task) {
      throw new Error(`上传任务不存在: ${taskId}`);
    }

    if (task.status === "uploading") {
      return; // 已在上传中
    }

    // 如果是文件夹但不为空，这种情况不应该出现在单个任务中
    if (task.isFolder && !task.isEmpty) {
      throw new Error(`非空文件夹不能作为单个上传任务: ${task.fileName}`);
    }

    try {
      let file;

      // 如果是空文件夹，创建一个空的可读流
      if (task.isFolder && task.isEmpty) {
        tusLogger.info(`为空文件夹创建TUS上传会话: ${task.fileName}`);
        // 创建一个空的可读流来模拟文件上传，这样可以获取upload_url
        const { Readable } = await import("stream");
        file = new Readable({
          read() {
            this.push(null); // 立即结束流，表示空内容
          },
        });
      } else {
        // 普通文件，创建文件流
        const fs = await import("fs");

        // 🔧 修复：在创建文件流前检查文件是否存在
        try {
          await fs.promises.access(task.filePath);
          file = fs.createReadStream(task.filePath);
        } catch (error: any) {
          if (error.code === "ENOENT") {
            throw new Error(`文件不存在: ${task.filePath}`);
          }
          throw error;
        }
      }

      // 创建 TUS 上传实例
      const uploadOptions: any = {
        endpoint: this.config.endpoint,
        retryDelays: this.config.retryDelays || [0, 1000, 3000, 5000],
        chunkSize: this.config.chunkSize || 20 * 1024 * 1024,
        metadata: {
          filename: task.fileName,
          filetype: task.isFolder && task.isEmpty ? "application/x-directory" : this.getFileType(task.fileName),
          isFolder: task.isFolder ? "true" : "false",
          isEmpty: task.isEmpty ? "true" : "false",
          ...task.metadata,
        },
        headers: this.config.headers || {},
      };

      // 如果是空文件夹，需要明确指定上传大小为0
      if (task.isFolder && task.isEmpty) {
        uploadOptions.uploadSize = 0;
        tusLogger.info(`设置空文件夹上传大小为0: ${task.fileName}`);
      }

      const upload = new Upload(file, {
        ...uploadOptions,

        // 进度回调
        onProgress: (bytesUploaded: number, bytesTotal: number) => {
          this.handleProgress(taskId, bytesUploaded, bytesTotal);
        },

        // 错误回调
        onError: (error: Error) => {
          this.handleError(taskId, error.message);
        },

        // 成功回调
        onSuccess: () => {
          this.handleSuccess(taskId);
        },

        // 分片上传开始前的回调
        onChunkComplete: (_chunkSize: number, bytesAccepted: number, bytesTotal: number) => {
          // 更新上传速度和剩余时间
          this.updateUploadMetrics(taskId, bytesAccepted, bytesTotal);
        },

        // 上传开始后的回调，保存upload URL
        onAfterResponse: (_req: any, _res: any) => {
          if (upload.url && !task.uploadUrl) {
            task.uploadUrl = upload.url;
            this.saveTaskToStore(task);
            tusLogger.info(`上传已开始，保存URL: ${task.fileName}, URL: ${upload.url}`);
          }
        },

        // 恢复上传前的回调
        onBeforeRequest: (req: any) => {
          // 添加自定义请求头
          if (this.config.headers) {
            Object.keys(this.config.headers).forEach((key) => {
              req.setHeader(key, this.config.headers![key]);
            });
          }

          // 动态添加认证头（优先使用全局authToken）
          const globalToken = getAuthToken();
          if (globalToken) {
            req.setHeader("Authorization", `Bearer ${globalToken}`);
          } else if (this.config.headers?.Authorization) {
            // 如果没有全局token，使用配置中的Authorization头
            tusLogger.info("🔐 使用配置中的认证头");
          } else {
            tusLogger.warn("⚠️ 上传请求缺少认证头，可能导致认证失败");
          }
        },
      });

      // 存储上传实例
      this.uploads.set(taskId, upload);

      // 如果有之前的上传 URL，尝试恢复
      if (task.uploadUrl) {
        upload.url = task.uploadUrl;
        tusLogger.info(`尝试恢复上传: ${task.fileName}, URL: ${task.uploadUrl}`);

        try {
          // 🔧 修复：添加错误处理和重试机制来处理Header overflow错误
          const previousUploads = await this.findPreviousUploadsWithRetry(upload, task);
          if (previousUploads.length > 0) {
            upload.resumeFromPreviousUpload(previousUploads[0]);
            tusLogger.info(`找到之前的上传记录，从 ${task.bytesUploaded} 字节开始恢复`);
          } else {
            tusLogger.warn(`未找到之前的上传记录，将从头开始上传: ${task.fileName}`);
          }
        } catch (error) {
          tusLogger.error(`恢复上传失败: ${task.fileName}`, error);
          // 如果恢复失败，清除uploadUrl并从头开始上传
          delete task.uploadUrl;
          upload.url = null;
          this.saveTaskToStore(task);
          tusLogger.info(`清除无效的上传URL，将从头开始上传: ${task.fileName}`);
        }
      }

      // 更新任务状态
      this.updateTaskStatus(taskId, "uploading");

      // 对于空文件夹，立即设置进度为100%，因为TUS可能不会触发onProgress回调
      if (task.isFolder && task.isEmpty) {
        tusLogger.info(`空文件夹上传开始，立即设置进度: ${task.fileName}`);
        // 手动触发进度更新，确保UI显示正确
        setTimeout(() => {
          this.handleProgress(taskId, 0, 0);
        }, 50);
      }

      // 开始上传
      upload.start();

      // 在上传开始后，等待一小段时间以确保URL已设置，然后保存
      setTimeout(() => {
        if (upload.url && !task.uploadUrl) {
          task.uploadUrl = upload.url;
          this.saveTaskToStore(task);
          tusLogger.info(`上传开始后保存URL: ${task.fileName}, URL: ${upload.url}`);
        }
      }, 100);
    } catch (error) {
      this.handleError(taskId, `启动上传失败: ${error}`);
    }
  }

  /**
   * 暂停上传
   */
  async pauseUpload(taskId: string): Promise<void> {
    const upload = this.uploads.get(taskId);
    const task = this.tasks.get(taskId);

    if (!upload || !task) {
      throw new Error(`上传任务不存在: ${taskId}`);
    }

    if (task.status !== "uploading") {
      return;
    }

    try {
      // 保存上传URL以便后续恢复
      if (upload.url) {
        task.uploadUrl = upload.url;
        this.saveTaskToStore(task);
        tusLogger.info(`暂停上传，保存URL: ${task.fileName}, URL: ${upload.url}, 已上传: ${task.bytesUploaded} 字节`);
      }

      await upload.abort(false); // false 表示不删除服务器上的临时文件
      this.updateTaskStatus(taskId, "paused");

      // 清理上传实例，但保留任务数据
      this.uploads.delete(taskId);
    } catch (error) {
      tusLogger.error("暂停上传失败:", error);
      this.updateTaskStatus(taskId, "paused"); // 即使出错也标记为暂停
    }
  }

  /**
   * 恢复上传
   */
  async resumeUpload(taskId: string): Promise<void> {
    const task = this.tasks.get(taskId);
    if (!task) {
      throw new Error(`上传任务不存在: ${taskId}`);
    }

    if (task.status !== "paused") {
      return;
    }

    // 重新开始上传
    await this.startUpload(taskId);
  }

  /**
   * 取消上传
   */
  async cancelUpload(taskId: string): Promise<void> {
    const upload = this.uploads.get(taskId);
    const task = this.tasks.get(taskId);

    if (!task) {
      throw new Error(`上传任务不存在: ${taskId}`);
    }

    try {
      if (upload && task.status === "uploading") {
        await upload.abort(true); // true 表示删除服务器上的临时文件
      }

      this.updateTaskStatus(taskId, "cancelled");
      this.cleanupTask(taskId);
    } catch (error) {
      tusLogger.error("取消上传失败:", error);
      this.updateTaskStatus(taskId, "cancelled");
    }
  }

  /**
   * 重试上传
   */
  async retryUpload(taskId: string): Promise<void> {
    const task = this.tasks.get(taskId);
    if (!task) {
      throw new Error(`上传任务不存在: ${taskId}`);
    }

    if (task.status !== "error") {
      return;
    }

    // 重置任务状态
    task.progress = 0;
    task.bytesUploaded = 0;
    task.error = undefined;
    task.startTime = new Date();

    this.updateTaskStatus(taskId, "pending");
    await this.startUpload(taskId);
  }

  /**
   * 删除任务
   */
  async deleteTask(taskId: string): Promise<void> {
    const upload = this.uploads.get(taskId);
    const task = this.tasks.get(taskId);

    if (upload && task?.status === "uploading") {
      await upload.abort(true);
    }

    this.cleanupTask(taskId);
  }

  /**
   * 获取所有任务
   */
  getAllTasks(): UploadTask[] {
    return Array.from(this.tasks.values());
  }

  /**
   * 获取指定任务
   */
  getTask(taskId: string): UploadTask | undefined {
    return this.tasks.get(taskId);
  }

  /**
   * 获取活跃任务（正在上传或暂停的任务）
   */
  getActiveTasks(): UploadTask[] {
    return this.getAllTasks().filter((task) => ["uploading", "paused", "pending"].includes(task.status));
  }

  /**
   * 获取指定任务的上传URL
   */
  getTaskUploadUrl(taskId: string): string | undefined {
    const task = this.tasks.get(taskId);
    if (task) {
      return task.uploadUrl;
    }

    // 如果任务不存在但有活跃的上传实例，也尝试获取URL
    const upload = this.uploads.get(taskId);
    return upload?.url || undefined;
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<TusUploadConfig>): void {
    this.config = { ...this.config, ...config };
    this.store.set("settings", {
      chunkSize: this.config.chunkSize,
      retryDelays: this.config.retryDelays,
      parallelUploads: this.config.parallelUploads,
    });
  }

  /**
   * 清理已完成的任务
   */
  clearCompletedTasks(): void {
    const completedTasks = this.getAllTasks().filter((task) => ["completed", "cancelled", "error"].includes(task.status));

    completedTasks.forEach((task) => {
      this.cleanupTask(task.id);
    });
  }

  /**
   * 清空所有当前任务（包括正在进行的任务）
   */
  async clearAllTasks(): Promise<void> {
    const allTasks = Array.from(this.tasks.values());
    tusLogger.info(`🧹 清空所有 ${allTasks.length} 个上传任务`);

    for (const task of allTasks) {
      try {
        if (["pending", "uploading", "paused"].includes(task.status)) {
          // 取消正在进行的任务
          await this.cancelUpload(task.id);
        } else {
          // 直接删除已完成的任务
          this.cleanupTask(task.id);
        }
      } catch (error) {
        tusLogger.error(`清空上传任务失败: ${task.fileName}`, error);
        // 即使失败也要删除
        this.cleanupTask(task.id);
      }
    }

    tusLogger.info(`✅ 已清空所有上传任务`);
  }

  /**
   * 智能打包分析
   */
  async analyzeSmartPacking(filePaths: string[], options?: BatchPackingOptions): Promise<SmartPackingAnalysis> {
    tusLogger.info(`📊 开始智能打包分析: ${filePaths.length} 个文件`);
    return await this.archiveManager.analyzeSmartPacking(filePaths, options);
  }

  /**
   * 智能打包上传
   */
  async smartPackUpload(
    filePaths: string[],
    options?: {
      threshold?: number;
      archiveName?: string;
      metadata?: Record<string, string>;
      skipConfirmation?: boolean;
      rootPath?: string;
    }
  ): Promise<{ success: boolean; taskId?: string; archiveTaskId?: string; error?: string }> {
    tusLogger.info(`📦 开始智能打包上传: ${filePaths.length} 个文件`);
    tusLogger.info(`📦 文件路径示例: ${filePaths.slice(0, 3).join(", ")}${filePaths.length > 3 ? "..." : ""}`);

    try {
      const threshold = options?.threshold || Number(process.env.VITE_SMART_PACKING_THRESHOLD) || 10;

      // 首先进行智能打包分析（仅基于文件数量，移除大小限制）
      const analysis = await this.analyzeSmartPacking(filePaths, {
        threshold,
      });

      tusLogger.info(`📊 智能打包分析结果:`, JSON.stringify(analysis));

      if (!analysis.shouldPack) {
        tusLogger.info(`❌ 不建议打包: ${analysis.reason}`);
        return {
          success: false,
          error: `不建议打包: ${analysis.reason}`,
        };
      }

      // 生成压缩包名称
      const archiveName = options?.archiveName || `batch_upload_${Date.now()}`;
      tusLogger.info(`📦 压缩包名称: ${archiveName}`);

      // 🔧 修复：检查是否已存在相同的智能打包任务
      const existingTask = this.findExistingSmartPackTask(filePaths, archiveName);
      if (existingTask) {
        tusLogger.info(`📦 发现已存在的智能打包任务: ${existingTask.fileName}, 返回现有任务`);
        return {
          success: true,
          taskId: existingTask.id,
        };
      }

      // 检查7zip-bin是否可用 - 这个检查现在由ArchiveManager处理
      // 如果ArchiveManager初始化成功，说明7zip-bin可用
      tusLogger.info(`📦 7zip-bin检查通过，由ArchiveManager管理`);

      // 验证ArchiveManager是否可用
      if (!this.archiveManager) {
        tusLogger.error(`❌ ArchiveManager未初始化`);
        return {
          success: false,
          error: `压缩管理器未初始化`,
        };
      }

      // 创建压缩任务
      tusLogger.info(`📦 创建压缩任务: ${archiveName}, 文件数: ${filePaths.length}`);
      const archiveTaskId = await this.archiveManager.createArchiveTask(filePaths, {
        name: archiveName,
        preserveStructure: true,
        rootPath: options?.rootPath,
        metadata: {
          ...options?.metadata,
          originalFileCount: filePaths.length.toString(),
          packingReason: analysis.reason,
          uploadType: "smart-packed",
        },
      });
      tusLogger.info(`📦 压缩任务已创建: ${archiveTaskId}`);

      // 🔧 修复：使用 Promise 和 once 监听器来避免重复触发和内存泄漏
      const compressionPromise = new Promise<{ success: boolean; taskId?: string; error?: string }>((resolve) => {
        const handleArchiveCompleted = async (taskId: string, result: any) => {
          if (taskId !== archiveTaskId) return;

          tusLogger.info(`📦 压缩任务完成: ${taskId}`, JSON.stringify(result));

          if (result.success && result.archivePath) {
            try {
              tusLogger.info(`📦 压缩成功，开始上传: ${result.archivePath}`);
              // 压缩成功，创建上传任务
              const uploadTaskId = await this.createUploadTask(result.archivePath, {
                ...options?.metadata,
                originalName: `${archiveName}.7z`,
                uploadType: "smart-packed-archive",
                originalFileCount: filePaths.length.toString(),
                compressionStats: JSON.stringify(result.stats),
                // 🔧 修复：保存原始文件列表和根路径，用于重新生成压缩文件
                originalFiles: JSON.stringify(filePaths),
                rootPath: options?.rootPath || "",
                archiveName: archiveName,
              });
              tusLogger.info(`📦 上传任务已创建: ${uploadTaskId}`);

              // 开始上传
              await this.startUpload(uploadTaskId);
              tusLogger.info(`📦 智能打包上传已开始: ${filePaths.length} 个文件打包为 ${archiveName}.7z`);

              resolve({ success: true, taskId: uploadTaskId });
            } catch (uploadError) {
              tusLogger.error("❌ 上传压缩包失败:", uploadError);
              resolve({ success: false, error: String(uploadError) });
            }
          } else {
            tusLogger.error("❌ 压缩失败:", result.error);
            resolve({ success: false, error: result.error });
          }
        };

        const handleArchiveError = (taskId: string, error: string) => {
          if (taskId !== archiveTaskId) return;

          tusLogger.error(`❌ 压缩任务失败: ${taskId}`, error);
          resolve({ success: false, error });
        };

        // 🔧 修复：使用 once 而不是 on 来避免重复触发
        this.archiveManager.once("task-completed", handleArchiveCompleted);
        this.archiveManager.once("task-error", handleArchiveError);
      });

      // 开始压缩
      tusLogger.info(`📦 开始压缩任务: ${archiveTaskId}`);
      await this.archiveManager.startArchiveTask(archiveTaskId);
      tusLogger.info(`📦 压缩任务已启动: ${archiveTaskId}`);

      // 等待压缩完成
      const result = await compressionPromise;

      return {
        success: result.success,
        taskId: result.taskId,
        archiveTaskId,
        error: result.error,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  // ========== 私有方法 ==========

  /**
   * 查找现有的智能打包任务
   */
  private findExistingSmartPackTask(filePaths: string[], archiveName: string): UploadTask | undefined {
    const filePathsSet = new Set(filePaths);

    for (const task of this.tasks.values()) {
      if (task.metadata?.uploadType === "smart-packed-archive" && task.metadata?.archiveName === archiveName && ["pending", "uploading", "paused"].includes(task.status)) {
        // 检查原始文件列表是否匹配
        try {
          const taskOriginalFiles = JSON.parse(task.metadata.originalFiles || "[]") as string[];
          const taskFilePathsSet = new Set(taskOriginalFiles);

          // 如果文件列表完全匹配，认为是重复任务
          if (taskFilePathsSet.size === filePathsSet.size && [...taskFilePathsSet].every((path: string) => filePathsSet.has(path))) {
            tusLogger.info(`📦 发现重复的智能打包任务: ${task.fileName}`);
            return task;
          }
        } catch (error) {
          tusLogger.warn(`解析任务原始文件列表失败: ${task.fileName}`, error);
        }
      }
    }

    return undefined;
  }

  /**
   * 带重试机制的查找之前上传记录
   * 用于处理Header overflow等网络错误
   */
  private async findPreviousUploadsWithRetry(upload: any, task: UploadTask, maxRetries: number = 3): Promise<any[]> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        tusLogger.info(`尝试查找之前的上传记录 (第${attempt}次): ${task.fileName}`);
        const previousUploads = await upload.findPreviousUploads();
        tusLogger.info(`成功查找到 ${previousUploads.length} 个之前的上传记录: ${task.fileName}`);
        return previousUploads;
      } catch (error: any) {
        lastError = error;
        const errorMessage = error.message || String(error);

        tusLogger.warn(`查找之前上传记录失败 (第${attempt}次): ${task.fileName}`, errorMessage);

        // 检查是否是Header overflow错误
        if (errorMessage.includes("Header overflow") || errorMessage.includes("Parse Error")) {
          tusLogger.warn(`检测到Header overflow错误，这可能是服务器响应头过大导致的`);

          // 如果是最后一次尝试，或者是Header overflow错误，直接抛出
          if (attempt === maxRetries) {
            throw new Error(`查找上传记录失败 (${maxRetries}次尝试): ${errorMessage}`);
          }

          // 等待一段时间后重试
          const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000); // 指数退避，最大5秒
          tusLogger.info(`等待 ${delay}ms 后重试...`);
          await new Promise((resolve) => setTimeout(resolve, delay));
        } else {
          // 其他类型的错误，直接抛出
          throw error;
        }
      }
    }

    // 如果所有重试都失败了，抛出最后一个错误
    throw lastError || new Error("查找上传记录失败：未知错误");
  }

  private generateTaskId(): string {
    return `upload_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * 启动智能打包文件清理定时器
   */
  private startSmartPackCleanupTimer(): void {
    // 每小时检查一次过期的智能打包文件
    setInterval(() => {
      this.cleanupExpiredSmartPackFiles().catch((error) => {
        tusLogger.error("清理过期智能打包文件失败:", error);
      });
    }, 60 * 60 * 1000); // 1小时

    // 启动时立即执行一次清理
    this.cleanupExpiredSmartPackFiles().catch((error) => {
      tusLogger.error("初始清理过期智能打包文件失败:", error);
    });
  }

  /**
   * 清理过期的智能打包文件
   */
  private async cleanupExpiredSmartPackFiles(): Promise<void> {
    try {
      const fs = await import("fs/promises");
      const path = await import("path");

      // 获取所有任务
      const allTasks = Array.from(this.tasks.values());
      const smartPackTasks = allTasks.filter((task) => task.metadata?.uploadType === "smart-packed-archive");

      // 获取所有活跃的智能打包文件路径
      const activeFilePaths = new Set(
        smartPackTasks
          .filter((task) => ["pending", "uploading", "paused"].includes(task.status))
          .map((task) => task.filePath)
          .filter(Boolean)
      );

      // 检查压缩文件目录
      // 使用应用数据目录中的temp-archives目录
      const { app } = await import("electron");
      const archiveDir = require("path").join(app.getPath("userData"), "temp-archives");

      try {
        await fs.access(archiveDir);
      } catch (error) {
        // 目录不存在，跳过清理
        return;
      }

      try {
        const files = await fs.readdir(archiveDir);
        const now = Date.now();
        const maxAge = 24 * 60 * 60 * 1000; // 24小时

        for (const file of files) {
          if (!file.endsWith(".7z")) {
            continue;
          }

          const filePath = path.join(archiveDir, file);

          // 如果文件正在被活跃任务使用，跳过
          if (activeFilePaths.has(filePath)) {
            continue;
          }

          try {
            const stats = await fs.stat(filePath);
            const age = now - stats.mtime.getTime();

            // 如果文件超过24小时且不被活跃任务使用，删除它
            if (age > maxAge) {
              await fs.unlink(filePath);
              tusLogger.info(`已清理过期智能打包文件: ${filePath} (年龄: ${Math.round(age / (60 * 60 * 1000))}小时)`);
            }
          } catch (error) {
            tusLogger.warn(`检查智能打包文件失败: ${filePath}`, error);
          }
        }
      } catch (error) {
        tusLogger.warn(`读取智能打包目录失败: ${archiveDir}`, error);
      }
    } catch (error) {
      tusLogger.error("清理过期智能打包文件异常:", error);
    }
  }

  private getFileType(fileName: string): string {
    const ext = path.extname(fileName).toLowerCase();
    const mimeTypes: Record<string, string> = {
      ".jpg": "image/jpeg",
      ".jpeg": "image/jpeg",
      ".png": "image/png",
      ".gif": "image/gif",
      ".mp4": "video/mp4",
      ".avi": "video/avi",
      ".mov": "video/quicktime",
      ".pdf": "application/pdf",
      ".doc": "application/msword",
      ".docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    };
    return mimeTypes[ext] || "application/octet-stream";
  }

  private handleProgress(taskId: string, bytesUploaded: number, bytesTotal: number): void {
    const task = this.tasks.get(taskId);
    if (!task) return;

    // 对于空文件夹（bytesTotal为0），直接设置进度为100%
    let progress: number;
    if (bytesTotal === 0) {
      progress = bytesUploaded === 0 ? 100 : 0;
      tusLogger.info(`空文件夹进度更新: ${task.fileName}, 进度: ${progress}%`);
    } else {
      progress = Math.round((bytesUploaded / bytesTotal) * 100);
    }

    task.progress = progress;
    task.bytesUploaded = bytesUploaded;

    this.saveTaskToStore(task);
    this.emit("task-progress", taskId, progress, bytesUploaded, bytesTotal);
  }

  private handleError(taskId: string, error: string): void {
    const task = this.tasks.get(taskId);
    if (!task) return;

    tusLogger.error(`上传任务失败: ${task.fileName}`, JSON.stringify(error));
    task.error = error;
    this.updateTaskStatus(taskId, "error");
    this.emit("task-error", taskId, error);
  }

  private handleSuccess(taskId: string): void {
    const task = this.tasks.get(taskId);
    const upload = this.uploads.get(taskId);
    if (!task) return;

    // 确保在成功时保存最终的上传URL
    if (upload?.url && !task.uploadUrl) {
      task.uploadUrl = upload.url;
      tusLogger.info(`上传成功，保存最终URL: ${task.fileName}, URL: ${upload.url}`);
    }

    task.progress = 100;
    // 对于空文件夹，bytesUploaded应该为0，对于普通文件应该为fileSize
    task.bytesUploaded = task.isFolder && task.isEmpty ? 0 : task.fileSize;
    this.updateTaskStatus(taskId, "completed");
    this.emit("task-completed", taskId);

    // 清理上传实例但保留任务记录（包含uploadUrl）
    this.uploads.delete(taskId);

    // 如果是临时文件，清理临时文件
    this.cleanupTempFile(task);
  }

  private updateTaskStatus(taskId: string, status: UploadStatus, error?: string): void {
    const task = this.tasks.get(taskId);
    if (!task) return;

    task.status = status;
    if (error) {
      task.error = error;
    }

    this.saveTaskToStore(task);
    this.emit("task-status-changed", taskId, status, error);
  }

  private updateUploadMetrics(taskId: string, bytesUploaded: number, bytesTotal: number): void {
    const task = this.tasks.get(taskId);
    if (!task) return;

    const currentTime = Date.now();
    const elapsedTime = currentTime - task.startTime.getTime();

    if (elapsedTime > 0) {
      // 计算上传速度 (bytes/s)
      task.uploadSpeed = Math.round(bytesUploaded / (elapsedTime / 1000));

      // 计算预计剩余时间 (秒)
      const remainingBytes = bytesTotal - bytesUploaded;
      task.remainingTime = task.uploadSpeed > 0 ? Math.round(remainingBytes / task.uploadSpeed) : 0;
    }
  }

  private saveTaskToStore(task: UploadTask): void {
    const tasks = this.store.get("tasks", {}) as Record<string, UploadTask>;
    tasks[task.id] = task;
    this.store.set("tasks", tasks);
  }

  private removeTaskFromStore(taskId: string): void {
    const tasks = this.store.get("tasks", {}) as Record<string, UploadTask>;
    delete tasks[taskId];
    this.store.set("tasks", tasks);
  }

  private cleanupTask(taskId: string): void {
    const task = this.tasks.get(taskId);

    // 清理临时文件
    if (task) {
      this.cleanupTempFile(task);
    }

    this.uploads.delete(taskId);
    this.tasks.delete(taskId);
    this.removeTaskFromStore(taskId);
  }

  /**
   * 清理临时文件
   */
  private async cleanupTempFile(task: UploadTask): Promise<void> {
    // 🔧 修复：智能打包的压缩文件不立即清理，保留用于断点续传
    if (task.metadata?.uploadType === "smart-packed-archive") {
      tusLogger.info(`智能打包文件保留用于断点续传: ${task.filePath}`);
      return;
    }

    if (task.metadata?.tempFilePath) {
      try {
        const fs = await import("fs/promises");
        // 🔧 修复：先检查文件是否存在，避免 ENOENT 错误
        try {
          await fs.access(task.metadata.tempFilePath);
          await fs.unlink(task.metadata.tempFilePath);
          tusLogger.info(`已清理临时文件: ${task.metadata.tempFilePath}`);
        } catch (accessError: any) {
          if (accessError.code === "ENOENT") {
            tusLogger.debug(`临时文件已不存在，跳过清理: ${task.metadata.tempFilePath}`);
          } else {
            throw accessError;
          }
        }
      } catch (error) {
        tusLogger.warn(`清理临时文件失败: ${task.metadata.tempFilePath}`, error);
      }
    }
  }

  private restoreUnfinishedTasks(): void {
    const storedTasks = this.store.get("tasks", {}) as Record<string, UploadTask>;
    const smartPackTasks: UploadTask[] = [];
    const regularTasks: UploadTask[] = [];

    // 🔧 修复：分离智能打包任务和普通任务
    Object.values(storedTasks).forEach((storedTask) => {
      if (["uploading", "paused", "pending", "completed"].includes(storedTask.status)) {
        if (storedTask.metadata?.uploadType === "smart-packed-archive") {
          smartPackTasks.push(storedTask);
        } else {
          regularTasks.push(storedTask);
        }
      }
    });

    // 恢复普通任务
    regularTasks.forEach((storedTask) => {
      // 将之前正在上传的任务标记为暂停
      if (storedTask.status === "uploading") {
        storedTask.status = "paused";
      }

      this.tasks.set(storedTask.id, {
        ...storedTask,
        startTime: new Date(storedTask.startTime),
      });

      tusLogger.info(`恢复普通任务: ${storedTask.fileName} (${storedTask.status})`);
    });

    // 🔧 修复：同步处理智能打包任务恢复，避免任务消失
    this.restoreSmartPackTasksSync(smartPackTasks);

    tusLogger.info(`已恢复 ${this.tasks.size} 个任务到内存 (普通任务: ${regularTasks.length}, 智能打包任务: ${smartPackTasks.length})`);
  }

  /**
   * 同步恢复智能打包任务，包含去重和状态验证
   */
  private restoreSmartPackTasksSync(smartPackTasks: UploadTask[]): void {
    const taskMap = new Map<string, UploadTask>();

    // 🔧 修复：基于 archiveName 和 originalFiles 进行去重
    for (const task of smartPackTasks) {
      const archiveName = task.metadata?.archiveName;
      const originalFiles = task.metadata?.originalFiles;

      if (!archiveName || !originalFiles) {
        // 缺少关键元数据，直接恢复
        this.restoreSingleSmartPackTaskSync(task);
        continue;
      }

      const key = `${archiveName}_${originalFiles}`;
      const existingTask = taskMap.get(key);

      if (!existingTask) {
        taskMap.set(key, task);
      } else {
        // 发现重复任务，保留最新的（基于创建时间）
        const existingTime = new Date(existingTask.startTime).getTime();
        const currentTime = new Date(task.startTime).getTime();

        if (currentTime > existingTime) {
          tusLogger.info(`📦 发现重复的智能打包任务，保留较新的: ${task.fileName}`);
          taskMap.set(key, task);
          // 清理旧任务
          this.removeTaskFromStore(existingTask.id);
        } else {
          tusLogger.info(`📦 发现重复的智能打包任务，保留较新的: ${existingTask.fileName}`);
          // 清理当前任务
          this.removeTaskFromStore(task.id);
        }
      }
    }

    // 恢复去重后的任务
    for (const task of taskMap.values()) {
      this.restoreSingleSmartPackTaskSync(task);
    }
  }

  /**
   * 同步恢复单个智能打包任务
   */
  private restoreSingleSmartPackTaskSync(storedTask: UploadTask): void {
    // 将之前正在上传的任务标记为暂停
    if (storedTask.status === "uploading") {
      storedTask.status = "paused";
    }

    // 🔧 修复：对于智能打包任务，先恢复到内存，文件验证延迟到实际使用时
    this.tasks.set(storedTask.id, {
      ...storedTask,
      startTime: new Date(storedTask.startTime),
    });

    tusLogger.info(`恢复智能打包任务: ${storedTask.fileName} (${storedTask.status})`);

    // 🔧 修复：异步验证文件存在性，但不阻塞任务恢复
    if (storedTask.filePath) {
      import("fs/promises")
        .then((fs) => {
          return fs.access(storedTask.filePath!);
        })
        .catch((error: any) => {
          if (error.code === "ENOENT") {
            tusLogger.warn(`智能打包文件不存在，将在启动时标记为错误: ${storedTask.fileName}`);
            // 更新任务状态为错误，但不从内存中移除
            const task = this.tasks.get(storedTask.id);
            if (task) {
              task.status = "error";
              task.error = "临时压缩文件已被清理，请重新上传";
              this.saveTaskToStore(task);
            }
          }
        });
    }
  }
}
